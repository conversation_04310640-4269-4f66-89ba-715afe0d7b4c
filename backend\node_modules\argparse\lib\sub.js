// Limited implementation of python % string operator, supports only %s and %r for now
// (other formats are not used here, but may appear in custom templates)

'use strict'

const { inspect } = require('util')


module.exports = function sub(pattern, ...values) {
    let regex = /%(?:(%)|(-)?(\*)?(?:\((\w+)\))?([A-Za-z]))/g

    let result = pattern.replace(regex, function (_, is_literal, is_left_align, is_padded, name, format) {
        if (is_literal) return '%'

        let padded_count = 0
        if (is_padded) {
            if (values.length === 0) throw new TypeError('not enough arguments for format string')
            padded_count = values.shift()
            if (!Number.isInteger(padded_count)) throw new TypeError('* wants int')
        }

        let str
        if (name !== undefined) {
            let dict = values[0]
            if (typeof dict !== 'object' || dict === null) throw new TypeError('format requires a mapping')
            if (!(name in dict)) throw new TypeError(`no such key: '${name}'`)
            str = dict[name]
        } else {
            if (values.length === 0) throw new TypeError('not enough arguments for format string')
            str = values.shift()
        }

        switch (format) {
            case 's':
                str = String(str)
                break
            case 'r':
                str = inspect(str)
                break
            case 'd':
            case 'i':
                if (typeof str !== 'number') {
                    throw new TypeError(`%${format} format: a number is required, not ${typeof str}`)
                }
                str = String(str.toFixed(0))
                break
            default:
                throw new TypeError(`unsupported format character '${format}'`)
        }

        if (padded_count > 0) {
            return is_left_align ? str.padEnd(padded_count) : str.padStart(padded_count)
        } else {
            return str
        }
    })

    if (values.length) {
        if (values.length === 1 && typeof values[0] === 'object' && values[0] !== null) {
            // mapping
        } else {
            throw new TypeError('not all arguments converted during string formatting')
        }
    }

    return result
}
